// Unit tests for gl_title_atr implementation
// Jest test file

describe('RealtyGamePropertyPage - gl_title_atr Implementation', () => {
  
  describe('Data Flattening', () => {
    it('should preserve gl_title_atr from parent game listing', () => {
      const mockGameListing = {
        uuid: 'game-listing-123',
        gl_title_atr: 'Game Specific Title',
        listing_details: {
          uuid: 'listing-456',
          title: 'Regular Property Title',
          city: 'London'
        }
      };

      // Simulate the flattening logic
      const flattened = {
        uuid: mockGameListing.uuid,
        ...mockGameListing.listing_details,
        parentGameListingUuid: mockGameListing.uuid,
        listing_details_uuid: mockGameListing.listing_details.uuid,
        gl_title_atr: mockGameListing.gl_title_atr
      };

      expect(flattened.gl_title_atr).toBe('Game Specific Title');
      expect(flattened.title).toBe('Regular Property Title');
      expect(flattened.uuid).toBe('game-listing-123');
    });
  });

  describe('Property Data Enhancement', () => {
    it('should enhance property data with gl_title_atr', () => {
      const mockPropertyData = {
        uuid: 'property-789',
        title: 'Original Property Title',
        price_sale_current_cents: 50000000
      };

      const mockGameListings = [
        {
          uuid: 'game-listing-123',
          listing_details_uuid: 'property-789',
          gl_title_atr: 'Enhanced Game Title'
        }
      ];

      // Simulate the enhancement logic
      const matchingGameListing = mockGameListings.find(
        listing => listing.listing_details_uuid === mockPropertyData.uuid
      );

      const enhancedProperty = {
        ...mockPropertyData,
        gl_title_atr: matchingGameListing?.gl_title_atr
      };

      expect(enhancedProperty.gl_title_atr).toBe('Enhanced Game Title');
      expect(enhancedProperty.title).toBe('Original Property Title');
    });
  });

  describe('Title Display Logic', () => {
    it('should show gl_title_atr when available', () => {
      const mockProperty = {
        title: 'Regular Title',
        gl_title_atr: 'Game Title'
      };

      const displayTitle = mockProperty.gl_title_atr || mockProperty.title;
      expect(displayTitle).toBe('Game Title');
    });

    it('should fallback to title when gl_title_atr is not available', () => {
      const mockProperty = {
        title: 'Regular Title',
        gl_title_atr: null
      };

      const displayTitle = mockProperty.gl_title_atr || mockProperty.title;
      expect(displayTitle).toBe('Regular Title');
    });

    it('should handle undefined gl_title_atr', () => {
      const mockProperty = {
        title: 'Regular Title'
        // gl_title_atr is undefined
      };

      const displayTitle = mockProperty.gl_title_atr || mockProperty.title;
      expect(displayTitle).toBe('Regular Title');
    });

    it('should handle empty string gl_title_atr', () => {
      const mockProperty = {
        title: 'Regular Title',
        gl_title_atr: ''
      };

      const displayTitle = mockProperty.gl_title_atr || mockProperty.title;
      expect(displayTitle).toBe('Regular Title');
    });
  });

  describe('Meta Tag Generation', () => {
    it('should use gl_title_atr in page title when available', () => {
      const enhancedPropertyData = {
        title: 'Regular Title',
        gl_title_atr: 'Game Title',
        city: 'London'
      };

      const pageTitle = `Can you guess the price of: ${
        enhancedPropertyData.gl_title_atr || enhancedPropertyData.title
      }, ${enhancedPropertyData.city}?`;

      expect(pageTitle).toBe('Can you guess the price of: Game Title, London?');
    });

    it('should fallback to regular title in page title', () => {
      const enhancedPropertyData = {
        title: 'Regular Title',
        city: 'London'
      };

      const pageTitle = `Can you guess the price of: ${
        enhancedPropertyData.gl_title_atr || enhancedPropertyData.title
      }, ${enhancedPropertyData.city}?`;

      expect(pageTitle).toBe('Can you guess the price of: Regular Title, London?');
    });
  });

  describe('Game Listing Matching', () => {
    it('should find matching game listing by uuid', () => {
      const flattenedGameListings = [
        { uuid: 'game-1', gl_title_atr: 'Game Title 1' },
        { uuid: 'game-2', gl_title_atr: 'Game Title 2' }
      ];

      const listingInGameUuid = 'game-2';
      
      const matchingGameListing = flattenedGameListings.find(
        listing => listing.uuid === listingInGameUuid
      );

      expect(matchingGameListing.gl_title_atr).toBe('Game Title 2');
    });

    it('should find matching game listing by listing_details_uuid', () => {
      const flattenedGameListings = [
        { 
          uuid: 'game-1', 
          listing_details_uuid: 'listing-1', 
          gl_title_atr: 'Game Title 1' 
        },
        { 
          uuid: 'game-2', 
          listing_details_uuid: 'listing-2', 
          gl_title_atr: 'Game Title 2' 
        }
      ];

      const propertyUuid = 'listing-2';
      
      const matchingGameListing = flattenedGameListings.find(
        listing => listing.listing_details_uuid === propertyUuid
      );

      expect(matchingGameListing.gl_title_atr).toBe('Game Title 2');
    });
  });

  describe('Data Storage', () => {
    it('should store gl_title_atr in guess data', () => {
      const currentProperty = {
        uuid: 'prop-123',
        title: 'Regular Title',
        gl_title_atr: 'Game Title',
        currency: 'GBP'
      };

      const guessData = {
        guess: 500000,
        score: 85,
        propertyTitle: currentProperty.gl_title_atr || currentProperty.title,
        currency: currentProperty.currency
      };

      expect(guessData.propertyTitle).toBe('Game Title');
    });

    it('should store gl_title_atr in feedback data', () => {
      const currentProperty = {
        uuid: 'prop-123',
        title: 'Regular Title',
        gl_title_atr: 'Game Title'
      };

      const feedbackData = {
        property_uuid: currentProperty.uuid,
        property_title: currentProperty.gl_title_atr || currentProperty.title,
        feedback_text: 'Great property!'
      };

      expect(feedbackData.property_title).toBe('Game Title');
    });
  });
});

// Integration test utilities
export const testUtilities = {
  createMockGameListing: (overrides = {}) => ({
    uuid: 'game-listing-123',
    gl_title_atr: 'Game Specific Title',
    listing_details: {
      uuid: 'listing-456',
      title: 'Regular Property Title',
      city: 'London',
      ...overrides.listing_details
    },
    ...overrides
  }),

  createMockProperty: (overrides = {}) => ({
    uuid: 'property-789',
    title: 'Regular Property Title',
    price_sale_current_cents: 50000000,
    ...overrides
  }),

  simulateDataFlattening: (gameListings) => {
    return gameListings.map(gListingItem => ({
      uuid: gListingItem.uuid,
      ...gListingItem.listing_details,
      parentGameListingUuid: gListingItem.uuid,
      listing_details_uuid: gListingItem.listing_details.uuid,
      gl_title_atr: gListingItem.gl_title_atr
    }));
  },

  simulatePropertyEnhancement: (propertyData, gameListings, listingInGameUuid) => {
    const matchingGameListing = gameListings.find(
      listing => listing.uuid === listingInGameUuid || 
                 listing.listing_details_uuid === propertyData.uuid
    );

    return {
      ...propertyData,
      gl_title_atr: matchingGameListing?.gl_title_atr
    };
  }
};
