# fetchAndSetGameData Function Documentation

## Overview

The `fetchAndSetGameData` function is a centralized utility that fetches game data from the API and sets it in the Pinia store. This function was centralized from multiple page components to eliminate code duplication and provide a consistent data fetching pattern across the application.

## Purpose

- **Centralization**: Eliminates code duplication by providing a single source of truth for game data fetching
- **Consistency**: Ensures consistent data structure and error handling across all game pages
- **SSR/Client Compatibility**: Works seamlessly in both Server-Side Rendering (SSR) and client-side contexts
- **Store Integration**: Directly integrates with the Pinia store for state management

## Function Signature

```javascript
const fetchAndSetGameData = async (gameSlug, store) => {
  // Implementation details...
}
```

### Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `gameSlug` | `string` | The unique identifier for the game to fetch data for |
| `store` | `Object` | Pinia store instance (typically `useRealtyGameStore()`) |

### Return Value

| Type | Description |
|------|-------------|
| `Object \| null` | Returns the processed store data object on success, or `null` if no data is available |

## API Endpoint

The function makes a GET request to:
```
/api_public/v4/realty_game_summary/{gameSlug}
```

## Data Processing Flow

1. **API Call**: Fetches raw game data from the realty game summary endpoint
2. **Data Extraction**: Extracts key components from the API response:
   - `price_guess_inputs.game_listings` - Array of property listings
   - `realty_game_details` - Game metadata (title, description, etc.)
   - `default_currency` - Game's default currency
3. **Filtering**: Filters visible properties only (`listing_details.visible === true`)
4. **Data Normalization**: Creates a standardized store data object
5. **Store Update**: Updates the Pinia store with the processed data
6. **Return**: Returns the processed data for further use

## Store Data Structure

The function creates and sets the following data structure in the store:

```javascript
const storeData = {
  gameListings: Array,           // Filtered visible game listings
  gameTitle: string,             // Game title or default fallback
  gameDesc: string,              // Game description or empty string
  gameBgImageUrl: string,        // Background image URL or empty string
  gameDefaultCurrency: string,   // Default currency or 'GBP'
  totalProperties: number,       // Count of visible properties
  currentProperty: null,         // Initially no current property
  isDataLoaded: true            // Flag indicating data is loaded
}
```

## Usage Examples

### In a Vue Page Component (preFetch)

```javascript
// In <script> block for SSR preFetch
import { useRealtyGame } from 'src/concerns/realty-game/composables/useRealtyGame'

export default {
  async preFetch({ currentRoute, ssrContext }) {
    const gameSlug = currentRoute.params.gameSlug
    if (!gameSlug) return

    try {
      const store = useRealtyGameStore()
      const { fetchAndSetGameData } = useRealtyGame()
      const gameData = await fetchAndSetGameData(gameSlug, store)
      
      if (gameData) {
        console.log(`Loaded ${gameData.totalProperties} properties`)
      }
    } catch (error) {
      console.error('Failed to load game data:', error)
    }
  }
}
```

### In a Vue Component (client-side)

```javascript
// In <script setup>
import { useRealtyGame } from 'src/concerns/realty-game/composables/useRealtyGame'
import { useRealtyGameStore } from 'src/stores/realtyGame'

const realtyGameStore = useRealtyGameStore()
const { fetchAndSetGameData } = useRealtyGame()

const initializeGame = async () => {
  try {
    const gameData = await fetchAndSetGameData(gameSlug.value, realtyGameStore)
    if (gameData) {
      // Game data loaded successfully
      console.log(`Game "${gameData.gameTitle}" loaded with ${gameData.totalProperties} properties`)
    }
  } catch (error) {
    console.error('Failed to load game data:', error)
  }
}
```

## Error Handling

The function does not include explicit error handling - it's expected that calling code will wrap calls in try-catch blocks. Common error scenarios include:

- **Network failures**: API endpoint unavailable
- **Invalid gameSlug**: Non-existent game identifier
- **Malformed response**: Unexpected API response structure
- **Store errors**: Issues with Pinia store updates

## Data Fallbacks

The function provides sensible defaults for missing data:

| Field | Default Value | Fallback Logic |
|-------|---------------|----------------|
| `gameTitle` | `'Property Price Challenge'` | Uses `realtyGameDetails?.game_title` or fallback |
| `gameDesc` | `''` (empty string) | Uses `realtyGameDetails?.game_description` or fallback |
| `gameBgImageUrl` | `''` (empty string) | Uses `realtyGameDetails?.game_bg_image_url` or fallback |
| `gameDefaultCurrency` | `'GBP'` | Uses `gameData?.default_currency` or fallback |
| `gameListings` | `[]` (empty array) | Uses filtered visible listings or fallback |

## Integration Points

### With Pinia Store

The function directly calls `store.setRealtyGameData(storeData)` to update the store state. This triggers reactivity throughout the application.

### With Page Components

Used in multiple page components including:
- `RealtyGameStartPage.vue`
- `RealtyGameResultsPage.vue`
- `RealtyGameResultsDetailedPage.vue`
- `OneOffGameStartPage.vue`
- `OneOffGameResultsDetailedPage.vue`
- `RoundupGameStartPage.vue`
- `RoundupGameResultsDetailedPage.vue`

### With Meta Management

Often used in conjunction with meta store updates for SEO:

```javascript
const gameData = await fetchAndSetGameData(gameSlug, store)
if (gameData) {
  metaStore.setMeta({
    title: `Property Price Challenge: ${gameData.gameTitle}`,
    description: gameData.gameDesc || 'Test your property knowledge',
  })
}
```

## Performance Considerations

- **Single API Call**: Makes only one API request per game
- **Efficient Filtering**: Filters out hidden properties at the data level
- **Store Caching**: Relies on store state for subsequent access
- **Memory Usage**: Loads all visible properties at once (not paginated)

## Testing Strategy

The function should be tested for:
1. **Successful data fetching**: With valid gameSlug and store
2. **Error handling**: With invalid gameSlug or network errors
3. **Data transformation**: Verify correct mapping from API to store format
4. **Store integration**: Ensure store is properly updated
5. **Fallback values**: Test default values when API data is missing
6. **Filtering logic**: Verify only visible properties are included

### Test Coverage

Comprehensive tests are available in:
- `__tests__/fetchAndSetGameData.test.js` - 17 unit tests covering all major scenarios
- `__tests__/fetchAndSetGameData.integration.test.js` - 10 integration tests with real store instances

**Key test scenarios:**
- ✅ Successful API calls with complete/partial data
- ✅ Error handling (network failures, 404s, store errors)
- ✅ Data filtering and transformation
- ✅ Edge cases (empty data, malformed responses)
- ✅ Store integration and reactivity
- ✅ Real-world usage patterns (SSR, client-side)
- ✅ Performance with large datasets
- ✅ Memory management and concurrent calls

Run tests with: `npm test -- src/concerns/realty-game/composables/__tests__/fetchAndSetGameData`

## Migration Notes

### Before Centralization

Each page component had its own copy of this function, leading to:
- Code duplication across 7+ components
- Inconsistent error handling
- Maintenance overhead
- Risk of divergent implementations

### After Centralization

- Single source of truth in `useRealtyGame` composable
- Consistent behavior across all components
- Easier maintenance and updates
- Reduced bundle size through elimination of duplicated code

## Dependencies

| Dependency | Purpose |
|------------|---------|
| `axios` | HTTP client for API requests |
| `pwbFlexConfig` | Configuration object containing API base URL |
| Pinia store | State management integration |

## Related Functions

- `setRealtyGameData()` - Store action that this function calls
- `fetchPriceGuessData()` - Alternative data fetching method
- `clearRealtyGameData()` - Store action to reset game data

## Future Enhancements

Potential improvements could include:
- **Caching mechanism**: Avoid refetching unchanged data
- **Progressive loading**: Load minimal data first, then enhance
- **Error retry logic**: Automatic retry on transient failures
- **Data validation**: Validate API response structure
- **Performance monitoring**: Track API response times
