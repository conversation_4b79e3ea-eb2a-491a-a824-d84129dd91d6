# fetchAndSetGameData Test Suite

This directory contains comprehensive tests for the centralized `fetchAndSetGameData` function.

## Test Files

### 1. `fetchAndSetGameData.test.js` - Unit Tests
Focused unit tests that test the function in isolation with mocked dependencies.

**Test Categories:**
- **Successful API responses**: Tests normal operation with various data scenarios
- **API response edge cases**: Tests handling of malformed or missing data
- **Error handling**: Tests propagation of network and store errors
- **API endpoint construction**: Tests URL building logic
- **Data filtering logic**: Tests visibility filtering of properties
- **Store integration**: Tests interaction with the Pinia store

**Coverage:** 17 test cases covering all major code paths and edge cases.

### 2. `fetchAndSetGameData.integration.test.js` - Integration Tests
Tests the function with real Pinia store instances to verify end-to-end behavior.

**Test Categories:**
- **Real Store Integration**: Tests with actual Pinia store instances
- **Error Recovery Scenarios**: Tests state consistency during errors
- **Real-world Usage Patterns**: Tests SSR and client-side scenarios
- **Performance and Memory**: Tests memory management and large datasets

**Coverage:** 10 test cases focusing on realistic usage scenarios.

## Running the Tests

### Run all fetchAndSetGameData tests:
```bash
npm test -- src/concerns/realty-game/composables/__tests__/fetchAndSetGameData
```

### Run unit tests only:
```bash
npm test -- src/concerns/realty-game/composables/__tests__/fetchAndSetGameData.test.js
```

### Run integration tests only:
```bash
npm test -- src/concerns/realty-game/composables/__tests__/fetchAndSetGameData.integration.test.js
```

## Test Coverage

The test suite provides comprehensive coverage of:

### ✅ Happy Path Scenarios
- Successful API calls with complete data
- Data filtering (visible properties only)
- Store updates with correct structure
- Return value consistency

### ✅ Edge Cases
- Empty or missing API response data
- Missing optional fields (uses defaults)
- All properties hidden (empty result)
- Malformed API responses

### ✅ Error Scenarios
- Network failures
- 404 errors for non-existent games
- Store update failures
- API response parsing errors

### ✅ Data Integrity
- Correct filtering of visible properties
- Proper data transformation from API to store format
- Consistent data structure across calls
- Memory management (no data leaks)

### ✅ Integration Scenarios
- SSR preFetch usage patterns
- Client-side initialization
- Store reactivity preservation
- Concurrent call handling
- Large dataset performance

## Test Data Structures

### Mock API Response Format
```javascript
{
  data: {
    price_guess_inputs: {
      game_listings: [
        {
          listing_details: {
            visible: true,
            uuid: 'property-uuid',
            title: 'Property Title',
            // ... other property fields
          }
        }
      ],
      default_currency: 'GBP'
    },
    realty_game_details: {
      game_title: 'Game Title',
      game_description: 'Game Description',
      game_bg_image_url: 'https://example.com/bg.jpg'
    }
  }
}
```

### Expected Store Data Format
```javascript
{
  gameListings: Array,           // Filtered visible game listings
  gameTitle: string,             // Game title or default
  gameDesc: string,              // Game description or empty
  gameBgImageUrl: string,        // Background image URL or empty
  gameDefaultCurrency: string,   // Default currency or 'GBP'
  totalProperties: number,       // Count of visible properties
  currentProperty: null,         // Initially null
  isDataLoaded: true            // Always true on success
}
```

## Mocking Strategy

### Dependencies Mocked:
- **axios**: HTTP client for API requests
- **pwbFlexConfig**: Configuration object for API base URL
- **Pinia store**: For unit tests (real store used in integration tests)

### Mock Patterns:
- `mockedAxios.get.mockResolvedValue()` for successful responses
- `mockedAxios.get.mockRejectedValue()` for error scenarios
- `vi.fn()` for store method mocking in unit tests

## Assertions Tested

### Function Behavior:
- Correct API endpoint construction
- Proper data filtering and transformation
- Store integration and updates
- Return value consistency
- Error propagation

### Store State:
- Data loading flags
- Property count accuracy
- Title and metadata updates
- Data structure integrity
- Reactivity preservation

### Performance:
- Execution time for large datasets
- Memory usage patterns
- Concurrent call handling
- State cleanup between calls

## Contributing

When adding new tests:

1. **Unit tests** go in `fetchAndSetGameData.test.js`
2. **Integration tests** go in `fetchAndSetGameData.integration.test.js`
3. Follow the existing test structure and naming conventions
4. Include both positive and negative test cases
5. Mock dependencies appropriately for unit tests
6. Use real dependencies for integration tests where beneficial

## Related Documentation

- [FETCHANDSETGAMEDATA.md](../FETCHANDSETGAMEDATA.md) - Function documentation
- [../../stores/README-realtyGame.md](../../stores/README-realtyGame.md) - Store documentation
- [../../../../vitest.config.js](../../../../vitest.config.js) - Test configuration
