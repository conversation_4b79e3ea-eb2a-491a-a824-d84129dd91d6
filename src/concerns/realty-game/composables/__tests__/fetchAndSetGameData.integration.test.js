import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { useRealtyGame } from '../useRealtyGame'
import { useRealtyGameStore } from 'src/stores/realtyGame'
import { createPinia, setActivePinia } from 'pinia'
import axios from 'axios'

// Mock axios and config
vi.mock('axios')
vi.mock('boot/pwb-flex-conf', () => ({
  pwbFlexConfig: {
    dataApiBase: 'https://api.example.com'
  }
}))

const mockedAxios = vi.mocked(axios)

describe('fetchAndSetGameData Integration Tests', () => {
  let pinia
  let store
  let fetchAndSetGameData

  beforeEach(() => {
    // Setup Pinia
    pinia = createPinia()
    setActivePinia(pinia)
    
    // Create actual store instance
    store = useRealtyGameStore()
    
    // Get the function from the composable
    const { fetchAndSetGameData: fn } = useRealtyGame()
    fetchAndSetGameData = fn
    
    // Reset mocks
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Real Store Integration', () => {
    it('should update actual Pinia store correctly', async () => {
      // Arrange
      const gameSlug = 'real-store-test'
      const apiResponse = {
        data: {
          price_guess_inputs: {
            game_listings: [
              {
                listing_details: {
                  visible: true,
                  uuid: 'property-1',
                  title: 'Beautiful London Flat',
                  price_sale_current_cents: 75000000,
                  city: 'London',
                  count_bedrooms: 2
                }
              },
              {
                listing_details: {
                  visible: true,
                  uuid: 'property-2',
                  title: 'Manchester House',
                  price_sale_current_cents: 45000000,
                  city: 'Manchester',
                  count_bedrooms: 3
                }
              }
            ],
            default_currency: 'GBP'
          },
          realty_game_details: {
            game_title: 'UK Property Challenge',
            game_description: 'Test your UK property market knowledge',
            game_bg_image_url: 'https://example.com/uk-bg.jpg'
          }
        }
      }

      mockedAxios.get.mockResolvedValue(apiResponse)

      // Verify initial store state
      expect(store.isDataLoaded).toBe(false)
      expect(store.gameListings).toEqual([])
      expect(store.gameTitle).toBe('Property Price Challenge')

      // Act
      const result = await fetchAndSetGameData(gameSlug, store)

      // Assert - Store state should be updated
      expect(store.isDataLoaded).toBe(true)
      expect(store.gameListings).toHaveLength(2)
      expect(store.gameTitle).toBe('UK Property Challenge')
      expect(store.gameDesc).toBe('Test your UK property market knowledge')
      expect(store.gameBgImageUrl).toBe('https://example.com/uk-bg.jpg')
      
      // Assert - Return value should match store data
      expect(result).toEqual({
        gameListings: store.gameListings,
        gameTitle: store.gameTitle,
        gameDesc: store.gameDesc,
        gameBgImageUrl: store.gameBgImageUrl,
        gameDefaultCurrency: 'GBP',
        totalProperties: 2,
        currentProperty: null,
        isDataLoaded: true
      })

      // Verify store getters work correctly
      expect(store.getTotalProperties).toBe(2)
      expect(store.getIsDataLoaded).toBe(true)
      
      // The store structure contains game listings with listing_details
      const firstProperty = store.gameListings[0]
      expect(firstProperty).toBeTruthy()
      expect(firstProperty.listing_details.uuid).toBe('property-1')
      expect(firstProperty.listing_details.title).toBe('Beautiful London Flat')
    })

    it('should handle subsequent calls correctly (data replacement)', async () => {
      // Arrange - First call
      const firstApiResponse = {
        data: {
          price_guess_inputs: {
            game_listings: [
              {
                listing_details: {
                  visible: true,
                  uuid: 'old-property',
                  title: 'Old Property'
                }
              }
            ]
          },
          realty_game_details: {
            game_title: 'Old Game'
          }
        }
      }

      mockedAxios.get.mockResolvedValueOnce(firstApiResponse)

      // Act - First call
      await fetchAndSetGameData('old-game', store)

      // Verify first call results
      expect(store.gameListings).toHaveLength(1)
      expect(store.gameTitle).toBe('Old Game')
      expect(store.gameListings[0].listing_details.uuid).toBe('old-property')

      // Arrange - Second call with different data
      const secondApiResponse = {
        data: {
          price_guess_inputs: {
            game_listings: [
              {
                listing_details: {
                  visible: true,
                  uuid: 'new-property-1',
                  title: 'New Property 1'
                }
              },
              {
                listing_details: {
                  visible: true,
                  uuid: 'new-property-2',
                  title: 'New Property 2'
                }
              }
            ]
          },
          realty_game_details: {
            game_title: 'New Game'
          }
        }
      }

      mockedAxios.get.mockResolvedValueOnce(secondApiResponse)

      // Act - Second call
      const result = await fetchAndSetGameData('new-game', store)

      // Assert - Store should contain new data, not old data
      expect(store.gameListings).toHaveLength(2)
      expect(store.gameTitle).toBe('New Game')
      
      // Check that old data is gone and new data is present
      const uuids = store.gameListings.map(listing => listing.listing_details.uuid)
      expect(uuids).not.toContain('old-property') // Old data should be gone
      expect(uuids).toContain('new-property-1')
      expect(uuids).toContain('new-property-2')
      
      expect(result.totalProperties).toBe(2)
      expect(result.gameTitle).toBe('New Game')
    })

    it('should preserve store reactivity after data loading', async () => {
      // Arrange
      const apiResponse = {
        data: {
          price_guess_inputs: {
            game_listings: [
              {
                listing_details: {
                  visible: true,
                  uuid: 'reactive-property',
                  title: 'Reactive Property'
                }
              }
            ]
          },
          realty_game_details: {
            game_title: 'Reactivity Test'
          }
        }
      }

      mockedAxios.get.mockResolvedValue(apiResponse)

      // Create a reactive watcher to test reactivity
      let watcherTriggered = false
      let watchedValue = null

      // Watch a computed property from the store
      const unwatch = store.$subscribe((mutation, state) => {
        watcherTriggered = true
        watchedValue = state.gameTitle
      })

      // Act
      await fetchAndSetGameData('reactive-test', store)

      // Assert
      expect(watcherTriggered).toBe(true)
      expect(watchedValue).toBe('Reactivity Test')

      // Cleanup
      unwatch()
    })
  })

  describe('Error Recovery Scenarios', () => {
    it('should not corrupt store state on API errors', async () => {
      // Arrange - Set initial valid state
      const initialApiResponse = {
        data: {
          price_guess_inputs: {
            game_listings: [
              {
                listing_details: {
                  visible: true,
                  uuid: 'initial-property',
                  title: 'Initial Property'
                }
              }
            ]
          },
          realty_game_details: {
            game_title: 'Initial Game'
          }
        }
      }

      mockedAxios.get.mockResolvedValueOnce(initialApiResponse)
      await fetchAndSetGameData('initial-game', store)

      // Verify initial state
      expect(store.gameListings).toHaveLength(1)
      expect(store.gameTitle).toBe('Initial Game')
      expect(store.isDataLoaded).toBe(true)

      // Arrange - Setup API error for second call
      mockedAxios.get.mockRejectedValueOnce(new Error('Network failure'))

      // Act & Assert - Second call should fail but not corrupt store
      await expect(fetchAndSetGameData('failing-game', store)).rejects.toThrow('Network failure')

      // Store should retain previous valid state
      expect(store.gameListings).toHaveLength(1)
      expect(store.gameTitle).toBe('Initial Game')
      expect(store.isDataLoaded).toBe(true)
      expect(store.gameListings[0].listing_details.uuid).toBe('initial-property')
    })

    it('should handle malformed API responses gracefully', async () => {
      // Arrange - Malformed response
      const malformedResponse = {
        data: {
          // Missing expected structure
          unexpected_field: 'unexpected_value',
          price_guess_inputs: null,
          realty_game_details: undefined
        }
      }

      mockedAxios.get.mockResolvedValue(malformedResponse)

      // Act
      const result = await fetchAndSetGameData('malformed-test', store)

      // Assert - Should handle gracefully with defaults
      expect(result).not.toBeNull()
      expect(result.gameListings).toEqual([])
      expect(result.gameTitle).toBe('Property Price Challenge')
      expect(result.gameDesc).toBe('')
      expect(result.gameBgImageUrl).toBe('')
      expect(result.gameDefaultCurrency).toBe('GBP')
      expect(result.totalProperties).toBe(0)

      // Store should be updated with safe defaults
      expect(store.isDataLoaded).toBe(true)
      expect(store.gameListings).toEqual([])
      expect(store.gameTitle).toBe('Property Price Challenge')
    })
  })

  describe('Real-world Usage Patterns', () => {
    it('should work in SSR preFetch context simulation', async () => {
      // Arrange - Simulate preFetch scenario
      const currentRoute = {
        params: {
          gameSlug: 'ssr-test-game'
        }
      }

      const apiResponse = {
        data: {
          price_guess_inputs: {
            game_listings: [
              {
                listing_details: {
                  visible: true,
                  uuid: 'ssr-property',
                  title: 'SSR Test Property',
                  price_sale_current_cents: 55000000
                }
              }
            ],
            default_currency: 'EUR'
          },
          realty_game_details: {
            game_title: 'SSR Game Challenge',
            game_description: 'Server-side rendered property game',
            game_bg_image_url: 'https://example.com/ssr-bg.jpg'
          }
        }
      }

      mockedAxios.get.mockResolvedValue(apiResponse)

      // Act - Simulate preFetch logic
      const gameSlug = currentRoute.params.gameSlug
      let preFetchResult = null
      let preFetchError = null

      try {
        preFetchResult = await fetchAndSetGameData(gameSlug, store)
      } catch (error) {
        preFetchError = error
      }

      // Assert - preFetch should succeed
      expect(preFetchError).toBeNull()
      expect(preFetchResult).toBeTruthy()
      expect(preFetchResult.gameTitle).toBe('SSR Game Challenge')

      // Store should be populated for subsequent client-side hydration
      expect(store.isDataLoaded).toBe(true)
      expect(store.gameListings).toHaveLength(1)
      
      // Note: gameDefaultCurrency is not stored in the store, only returned by the function
      expect(preFetchResult.gameDefaultCurrency).toBe('EUR')
    })

    it('should work in client-side initialization pattern', async () => {
      // Arrange - Simulate client-side mount scenario
      const apiResponse = {
        data: {
          price_guess_inputs: {
            game_listings: [
              {
                listing_details: {
                  visible: true,
                  uuid: 'client-property-1',
                  title: 'Client Property 1'
                }
              },
              {
                listing_details: {
                  visible: true,
                  uuid: 'client-property-2',
                  title: 'Client Property 2'
                }
              }
            ]
          },
          realty_game_details: {
            game_title: 'Client-side Game'
          }
        }
      }

      mockedAxios.get.mockResolvedValue(apiResponse)

      // Simulate checking if data needs to be loaded
      expect(store.isDataLoaded).toBe(false)

      // Act - Client-side initialization
      const gameSlug = 'client-game'
      let initializationResult = null

      if (!store.isDataLoaded) {
        initializationResult = await fetchAndSetGameData(gameSlug, store)
      }

      // Assert
      expect(initializationResult).toBeTruthy()
      expect(store.isDataLoaded).toBe(true)
      expect(store.gameListings).toHaveLength(2)

      // Subsequent check should not require refetch
      expect(store.isDataLoaded).toBe(true)
    })

    it('should handle concurrent calls correctly', async () => {
      // Arrange - Multiple concurrent API responses
      const apiResponse1 = {
        data: {
          price_guess_inputs: { game_listings: [] },
          realty_game_details: { game_title: 'Concurrent Game 1' }
        }
      }

      const apiResponse2 = {
        data: {
          price_guess_inputs: { game_listings: [] },
          realty_game_details: { game_title: 'Concurrent Game 2' }
        }
      }

      // Setup staggered responses
      mockedAxios.get
        .mockResolvedValueOnce(apiResponse1)
        .mockResolvedValueOnce(apiResponse2)

      // Act - Make concurrent calls
      const [result1, result2] = await Promise.all([
        fetchAndSetGameData('game1', store),
        fetchAndSetGameData('game2', store)
      ])

      // Assert - Both calls should succeed
      expect(result1).toBeTruthy()
      expect(result2).toBeTruthy()
      
      // Store should contain data from the last completed call
      // (exact result depends on which promise resolves last)
      expect(store.isDataLoaded).toBe(true)
      expect(['Concurrent Game 1', 'Concurrent Game 2']).toContain(store.gameTitle)
    })
  })

  describe('Performance and Memory', () => {
    it('should not leak memory on repeated calls', async () => {
      // Arrange
      const createApiResponse = (index) => ({
        data: {
          price_guess_inputs: {
            game_listings: [
              {
                listing_details: {
                  visible: true,
                  uuid: `property-${index}`,
                  title: `Property ${index}`
                }
              }
            ]
          },
          realty_game_details: {
            game_title: `Game ${index}`
          }
        }
      })

      // Act - Make multiple calls to simulate memory usage
      for (let i = 0; i < 10; i++) {
        mockedAxios.get.mockResolvedValueOnce(createApiResponse(i))
        await fetchAndSetGameData(`game-${i}`, store)
      }

      // Assert - Store should only contain data from the last call
      expect(store.gameListings).toHaveLength(1)
      expect(store.gameTitle).toBe('Game 9')
      
      // Check that only the last property is present
      const lastProperty = store.gameListings[0]
      expect(lastProperty.listing_details.uuid).toBe('property-9')
      
      // Verify previous data is not present
      const uuids = store.gameListings.map(listing => listing.listing_details.uuid)
      expect(uuids).not.toContain('property-0') // Previous data should be gone
    })

    it('should handle large datasets efficiently', async () => {
      // Arrange - Large dataset
      const largeGameListings = Array.from({ length: 100 }, (_, index) => ({
        listing_details: {
          visible: index % 2 === 0, // Only even indices are visible
          uuid: `large-property-${index}`,
          title: `Large Property ${index}`,
          price_sale_current_cents: (index + 1) * 1000000
        }
      }))

      const apiResponse = {
        data: {
          price_guess_inputs: {
            game_listings: largeGameListings
          },
          realty_game_details: {
            game_title: 'Large Dataset Game'
          }
        }
      }

      mockedAxios.get.mockResolvedValue(apiResponse)

      // Act
      const startTime = performance.now()
      const result = await fetchAndSetGameData('large-game', store)
      const endTime = performance.now()

      // Assert - Should filter correctly and perform reasonably
      expect(result.gameListings).toHaveLength(50) // Only visible properties
      expect(result.totalProperties).toBe(50)
      expect(endTime - startTime).toBeLessThan(100) // Should complete quickly

      // Verify filtering worked correctly
      result.gameListings.forEach((listing, index) => {
        expect(listing.listing_details.visible).toBe(true)
        expect(listing.listing_details.uuid).toMatch(/large-property-\d+/)
      })
    })
  })
})
