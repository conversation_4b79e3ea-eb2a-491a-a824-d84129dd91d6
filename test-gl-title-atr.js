// Test script for gl_title_atr implementation
// Run this in browser console on a RealtyGamePropertyPage

console.log('🧪 Testing gl_title_atr Implementation');
console.log('=====================================');

// Test 1: Check if currentProperty has gl_title_atr
const currentProperty = document.querySelector('.realty-game-property-page')?.__vueParentComponent?.ctx?.currentProperty;

if (currentProperty) {
  console.log('✅ Current Property found');
  console.log('   - UUID:', currentProperty.uuid);
  console.log('   - Title:', currentProperty.title);
  console.log('   - GL Title Attr:', currentProperty.gl_title_atr);
  console.log('   - Has GL Title:', !!currentProperty.gl_title_atr ? '✅' : '❌');
} else {
  console.log('❌ Current Property not found');
}

// Test 2: Check displayed title in DOM
const titleElement = document.querySelector('.property-header h1');
if (titleElement) {
  console.log('✅ Title Element found');
  console.log('   - Displayed Text:', titleElement.textContent.trim());
  
  if (currentProperty?.gl_title_atr) {
    const isCorrectTitle = titleElement.textContent.trim() === currentProperty.gl_title_atr;
    console.log('   - Shows GL Title:', isCorrectTitle ? '✅' : '❌');
  } else {
    const isCorrectTitle = titleElement.textContent.trim() === currentProperty?.title;
    console.log('   - Shows Regular Title:', isCorrectTitle ? '✅' : '❌');
  }
} else {
  console.log('❌ Title Element not found');
}

// Test 3: Check page meta title
const pageTitle = document.title;
console.log('✅ Page Meta Title:', pageTitle);

if (currentProperty?.gl_title_atr) {
  const hasGlTitle = pageTitle.includes(currentProperty.gl_title_atr);
  console.log('   - Uses GL Title in Meta:', hasGlTitle ? '✅' : '❌');
}

// Test 4: Check Vue component data
const vueApp = document.querySelector('#q-app')?.__vue_app__;
if (vueApp) {
  console.log('✅ Vue App found');
  
  // Try to find the property page component
  const findComponent = (component, name) => {
    if (component?.type?.name === name) return component;
    if (component?.subTree?.children) {
      for (const child of component.subTree.children) {
        if (child?.component) {
          const found = findComponent(child.component, name);
          if (found) return found;
        }
      }
    }
    return null;
  };
  
  const propertyPageComponent = findComponent(vueApp._instance, 'RealtyGamePropertyPage');
  if (propertyPageComponent) {
    console.log('✅ RealtyGamePropertyPage component found');
    const componentData = propertyPageComponent.setupState || propertyPageComponent.ctx;
    if (componentData?.currentProperty) {
      console.log('   - Component currentProperty:', componentData.currentProperty.title);
      console.log('   - Component gl_title_atr:', componentData.currentProperty.gl_title_atr);
    }
  }
}

// Test 5: Check fallback behavior
console.log('\n🔧 Fallback Test:');
const testFallback = (glTitle, regularTitle) => {
  const result = glTitle || regularTitle;
  return result;
};

console.log('   - With GL Title:', testFallback('Game Title', 'Regular Title'));
console.log('   - Without GL Title:', testFallback(null, 'Regular Title'));
console.log('   - Both null:', testFallback(null, null));

// Test 6: Check store data
try {
  const storeData = JSON.parse(localStorage.getItem('realtyGame') || '{}');
  if (storeData.currentProperty) {
    console.log('✅ Store Data found');
    console.log('   - Store GL Title:', storeData.currentProperty.gl_title_atr);
  }
} catch (e) {
  console.log('❌ Store Data not accessible');
}

console.log('\n📊 Test Summary:');
console.log('================');
console.log('Run this script on different property pages to verify:');
console.log('1. Properties with gl_title_atr show game title');
console.log('2. Properties without gl_title_atr show regular title');
console.log('3. Meta tags use correct title');
console.log('4. Component state is consistent');

// Helper function to manually test the fallback pattern
window.testGlTitleFallback = function(obj) {
  return obj?.gl_title_atr || obj?.title || 'No title available';
};

console.log('\n🛠️  Manual Test Helper:');
console.log('Use: testGlTitleFallback(currentProperty)');
